#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误处理和数据验证模块
提供统一的错误处理和数据验证功能
"""

import pandas as pd
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
import traceback

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """数据验证错误"""
    pass

class FileError(Exception):
    """文件操作错误"""
    pass

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_file_path(file_path: str) -> Tuple[bool, str]:
        """验证文件路径"""
        try:
            if not file_path:
                return False, "文件路径为空"
            
            path = Path(file_path)
            
            if not path.exists():
                return False, f"文件不存在: {file_path}"
            
            if not path.is_file():
                return False, f"路径不是文件: {file_path}"
            
            if path.suffix.lower() != '.csv':
                return False, f"文件格式不正确，需要CSV文件: {file_path}"
            
            # 检查文件大小
            file_size = path.stat().st_size
            if file_size == 0:
                return False, f"文件为空: {file_path}"
            
            # 检查文件大小限制（100MB）
            max_size = 100 * 1024 * 1024
            if file_size > max_size:
                return False, f"文件过大 ({file_size / 1024 / 1024:.1f}MB)，最大支持100MB"
            
            return True, "文件验证通过"
            
        except Exception as e:
            return False, f"文件验证失败: {str(e)}"
    
    @staticmethod
    def validate_csv_structure(df: pd.DataFrame, required_columns: List[str]) -> Tuple[bool, str]:
        """验证CSV结构"""
        try:
            if df is None:
                return False, "数据为空"
            
            if df.empty:
                return False, "CSV文件没有数据行"
            
            # 检查必需列
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return False, f"缺少必需列: {', '.join(missing_columns)}"
            
            # 检查空值
            for col in required_columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    return False, f"列 '{col}' 包含 {null_count} 个空值"
            
            # 检查数据类型
            for col in required_columns:
                if col in ['used', 'id']:
                    # 数值列检查
                    try:
                        pd.to_numeric(df[col], errors='raise')
                    except:
                        return False, f"列 '{col}' 包含非数值数据"
            
            return True, "CSV结构验证通过"
            
        except Exception as e:
            return False, f"CSV结构验证失败: {str(e)}"
    
    @staticmethod
    def validate_data_values(df: pd.DataFrame, mode: str) -> Tuple[bool, str]:
        """验证数据值"""
        try:
            if mode == 'user':
                # 用户模式验证
                if 'used' in df.columns:
                    invalid_used = df[~df['used'].isin([0, 1])]
                    if not invalid_used.empty:
                        return False, f"used列包含无效值，只能是0或1"
                
                if 'id' in df.columns:
                    invalid_id = df[~df['id'].isin([0, 1, 2, 3, 4, 5, 6, 7, 8])]
                    if not invalid_id.empty:
                        return False, f"id列包含无效值，只能是0-8的整数"
                
                # 检查用户名和全名的长度
                if 'username' in df.columns:
                    long_usernames = df[df['username'].str.len() > 100]
                    if not long_usernames.empty:
                        return False, "用户名长度不能超过100个字符"
                
                if 'fullname' in df.columns:
                    long_fullnames = df[df['fullname'].str.len() > 200]
                    if not long_fullnames.empty:
                        return False, "全名长度不能超过200个字符"
            
            elif mode == 'link':
                # 链接模式验证
                if 'used' in df.columns:
                    invalid_used = df[~df['used'].isin([0, 1])]
                    if not invalid_used.empty:
                        return False, f"used列包含无效值，只能是0或1"
                
                if 'id' in df.columns:
                    invalid_id = df[~df['id'].isin([0, 1, 2, 3, 4, 5, 6, 7, 8])]
                    if not invalid_id.empty:
                        return False, f"id列包含无效值，只能是0-8的整数"
                
                # 检查URL格式
                if 'url' in df.columns:
                    # 简单的URL格式检查
                    invalid_urls = df[~df['url'].str.contains(r'^https?://', na=False)]
                    if not invalid_urls.empty:
                        return False, "URL必须以http://或https://开头"
                    
                    # 检查URL长度
                    long_urls = df[df['url'].str.len() > 2000]
                    if not long_urls.empty:
                        return False, "URL长度不能超过2000个字符"
            
            return True, "数据值验证通过"
            
        except Exception as e:
            return False, f"数据值验证失败: {str(e)}"

class ErrorHandler:
    """错误处理器"""
    
    @staticmethod
    def handle_file_error(func):
        """文件操作错误装饰器"""
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except FileNotFoundError as e:
                error_msg = f"文件未找到: {str(e)}"
                logger.error(error_msg)
                return False, error_msg
            except PermissionError as e:
                error_msg = f"文件权限错误: {str(e)}"
                logger.error(error_msg)
                return False, error_msg
            except pd.errors.EmptyDataError as e:
                error_msg = "CSV文件为空或格式错误"
                logger.error(error_msg)
                return False, error_msg
            except pd.errors.ParserError as e:
                error_msg = f"CSV文件解析错误: {str(e)}"
                logger.error(error_msg)
                return False, error_msg
            except UnicodeDecodeError as e:
                error_msg = f"文件编码错误: {str(e)}"
                logger.error(error_msg)
                return False, error_msg
            except Exception as e:
                error_msg = f"文件操作失败: {str(e)}"
                logger.error(f"{error_msg}\n{traceback.format_exc()}")
                return False, error_msg
        return wrapper
    
    @staticmethod
    def handle_data_error(func):
        """数据处理错误装饰器"""
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ValidationError as e:
                error_msg = f"数据验证错误: {str(e)}"
                logger.error(error_msg)
                return False, error_msg
            except pd.errors.DataError as e:
                error_msg = f"数据处理错误: {str(e)}"
                logger.error(error_msg)
                return False, error_msg
            except KeyError as e:
                error_msg = f"数据列不存在: {str(e)}"
                logger.error(error_msg)
                return False, error_msg
            except ValueError as e:
                error_msg = f"数据值错误: {str(e)}"
                logger.error(error_msg)
                return False, error_msg
            except Exception as e:
                error_msg = f"数据处理失败: {str(e)}"
                logger.error(f"{error_msg}\n{traceback.format_exc()}")
                return False, error_msg
        return wrapper
    
    @staticmethod
    def log_operation(operation_name: str):
        """操作日志装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                logger.info(f"开始执行操作: {operation_name}")
                try:
                    result = func(*args, **kwargs)
                    logger.info(f"操作完成: {operation_name}")
                    return result
                except Exception as e:
                    logger.error(f"操作失败: {operation_name}, 错误: {str(e)}")
                    raise
            return wrapper
        return decorator

def safe_file_operation(operation_func, *args, **kwargs):
    """安全的文件操作"""
    try:
        return operation_func(*args, **kwargs)
    except Exception as e:
        logger.error(f"文件操作失败: {str(e)}")
        return None

def validate_and_clean_data(df: pd.DataFrame, mode: str) -> Tuple[pd.DataFrame, List[str]]:
    """验证并清理数据"""
    warnings = []
    cleaned_df = df.copy()
    
    try:
        # 去除空白字符
        for col in cleaned_df.select_dtypes(include=['object']).columns:
            cleaned_df[col] = cleaned_df[col].astype(str).str.strip()
        
        # 去除完全空白的行
        before_count = len(cleaned_df)
        cleaned_df = cleaned_df.dropna(how='all')
        after_count = len(cleaned_df)
        
        if before_count != after_count:
            warnings.append(f"已移除 {before_count - after_count} 个空行")
        
        # 数据类型转换
        if 'used' in cleaned_df.columns:
            cleaned_df['used'] = pd.to_numeric(cleaned_df['used'], errors='coerce').fillna(0).astype(int)
        
        if 'id' in cleaned_df.columns:
            cleaned_df['id'] = pd.to_numeric(cleaned_df['id'], errors='coerce').fillna(0).astype(int)
        
        return cleaned_df, warnings
        
    except Exception as e:
        logger.error(f"数据清理失败: {str(e)}")
        return df, [f"数据清理失败: {str(e)}"]
