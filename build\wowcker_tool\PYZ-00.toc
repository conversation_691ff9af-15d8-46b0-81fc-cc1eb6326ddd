('D:\\wowcker\\wowcker_tool\\build\\wowcker_tool\\PYZ-00.pyz',
 [('__future__', 'D:\\Anaconda3\\envs\\tool\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\Anaconda3\\envs\\tool\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Anaconda3\\envs\\tool\\lib\\_compression.py',
   'PYMODULE'),
  ('_dummy_thread',
   'D:\\Anaconda3\\envs\\tool\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Anaconda3\\envs\\tool\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Anaconda3\\envs\\tool\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\Anaconda3\\envs\\tool\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Anaconda3\\envs\\tool\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Anaconda3\\envs\\tool\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Anaconda3\\envs\\tool\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\Anaconda3\\envs\\tool\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Anaconda3\\envs\\tool\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Anaconda3\\envs\\tool\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Anaconda3\\envs\\tool\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Anaconda3\\envs\\tool\\lib\\calendar.py', 'PYMODULE'),
  ('cmd', 'D:\\Anaconda3\\envs\\tool\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Anaconda3\\envs\\tool\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Anaconda3\\envs\\tool\\lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'D:\\Anaconda3\\envs\\tool\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Anaconda3\\envs\\tool\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Anaconda3\\envs\\tool\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Anaconda3\\envs\\tool\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Anaconda3\\envs\\tool\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Anaconda3\\envs\\tool\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Anaconda3\\envs\\tool\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\Anaconda3\\envs\\tool\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\Anaconda3\\envs\\tool\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Anaconda3\\envs\\tool\\lib\\csv.py', 'PYMODULE'),
  ('csv_processor', 'D:\\wowcker\\wowcker_tool\\csv_processor.py', 'PYMODULE'),
  ('ctypes', 'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('data_allocator',
   'D:\\wowcker\\wowcker_tool\\data_allocator.py',
   'PYMODULE'),
  ('data_models', 'D:\\wowcker\\wowcker_tool\\data_models.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Anaconda3\\envs\\tool\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\Anaconda3\\envs\\tool\\lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'D:\\Anaconda3\\envs\\tool\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Anaconda3\\envs\\tool\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Anaconda3\\envs\\tool\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\Anaconda3\\envs\\tool\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\Anaconda3\\envs\\tool\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\utils.py',
   'PYMODULE'),
  ('error_handler', 'D:\\wowcker\\wowcker_tool\\error_handler.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Anaconda3\\envs\\tool\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Anaconda3\\envs\\tool\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\Anaconda3\\envs\\tool\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Anaconda3\\envs\\tool\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Anaconda3\\envs\\tool\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Anaconda3\\envs\\tool\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Anaconda3\\envs\\tool\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Anaconda3\\envs\\tool\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Anaconda3\\envs\\tool\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Anaconda3\\envs\\tool\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Anaconda3\\envs\\tool\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Anaconda3\\envs\\tool\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\Anaconda3\\envs\\tool\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'D:\\Anaconda3\\envs\\tool\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Anaconda3\\envs\\tool\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Anaconda3\\envs\\tool\\lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Anaconda3\\envs\\tool\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Anaconda3\\envs\\tool\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\Anaconda3\\envs\\tool\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\Anaconda3\\envs\\tool\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Anaconda3\\envs\\tool\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Anaconda3\\envs\\tool\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\Anaconda3\\envs\\tool\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\Anaconda3\\envs\\tool\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Anaconda3\\envs\\tool\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Anaconda3\\envs\\tool\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Anaconda3\\envs\\tool\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\Anaconda3\\envs\\tool\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\Anaconda3\\envs\\tool\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\Anaconda3\\envs\\tool\\lib\\optparse.py', 'PYMODULE'),
  ('pandas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.chainmap',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\chainmap.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.describe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\describe.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.numeric',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\numeric.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.date_converters',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\date_converters.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._xlwt',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_xlwt.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.compat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\compat.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.testing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\testing.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Anaconda3\\envs\\tool\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Anaconda3\\envs\\tool\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Anaconda3\\envs\\tool\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Anaconda3\\envs\\tool\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Anaconda3\\envs\\tool\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\Anaconda3\\envs\\tool\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\Anaconda3\\envs\\tool\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\Anaconda3\\envs\\tool\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Anaconda3\\envs\\tool\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Anaconda3\\envs\\tool\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\Anaconda3\\envs\\tool\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Anaconda3\\envs\\tool\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Anaconda3\\envs\\tool\\lib\\random.py', 'PYMODULE'),
  ('runpy', 'D:\\Anaconda3\\envs\\tool\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Anaconda3\\envs\\tool\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\Anaconda3\\envs\\tool\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\Anaconda3\\envs\\tool\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Anaconda3\\envs\\tool\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Anaconda3\\envs\\tool\\lib\\signal.py', 'PYMODULE'),
  ('six', 'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('socket', 'D:\\Anaconda3\\envs\\tool\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Anaconda3\\envs\\tool\\lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Anaconda3\\envs\\tool\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Anaconda3\\envs\\tool\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Anaconda3\\envs\\tool\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\Anaconda3\\envs\\tool\\lib\\ssl.py', 'PYMODULE'),
  ('string', 'D:\\Anaconda3\\envs\\tool\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Anaconda3\\envs\\tool\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Anaconda3\\envs\\tool\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\Anaconda3\\envs\\tool\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\Anaconda3\\envs\\tool\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Anaconda3\\envs\\tool\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Anaconda3\\envs\\tool\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Anaconda3\\envs\\tool\\lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'D:\\Anaconda3\\envs\\tool\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Anaconda3\\envs\\tool\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Anaconda3\\envs\\tool\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\Anaconda3\\envs\\tool\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Anaconda3\\envs\\tool\\lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib', 'D:\\Anaconda3\\envs\\tool\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'D:\\Anaconda3\\envs\\tool\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Anaconda3\\envs\\tool\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Anaconda3\\envs\\tool\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Anaconda3\\envs\\tool\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'D:\\Anaconda3\\envs\\tool\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'D:\\Anaconda3\\envs\\tool\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Anaconda3\\envs\\tool\\lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'D:\\Anaconda3\\envs\\tool\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\Anaconda3\\envs\\tool\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Anaconda3\\envs\\tool\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Anaconda3\\envs\\tool\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\Anaconda3\\envs\\tool\\lib\\zipimport.py', 'PYMODULE')])
