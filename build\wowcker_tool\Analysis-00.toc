(['D:\\wowcker\\wowcker_tool\\main.py'],
 ['D:\\wowcker\\wowcker_tool'],
 [],
 [('D:\\Anaconda3\\envs\\tool\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.8.20 (default, Oct  3 2024, 15:19:54) [MSC v.1929 64 bit (AMD64)]',
 [('pyi_rth_pkgutil',
   'D:\\Anaconda3\\envs\\tool\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Anaconda3\\envs\\tool\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Anaconda3\\envs\\tool\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\Anaconda3\\envs\\tool\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'D:\\wowcker\\wowcker_tool\\main.py', 'PYSOURCE')],
 [('subprocess', 'D:\\Anaconda3\\envs\\tool\\lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'D:\\Anaconda3\\envs\\tool\\lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'D:\\Anaconda3\\envs\\tool\\lib\\contextlib.py', 'PYMODULE'),
  ('threading', 'D:\\Anaconda3\\envs\\tool\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Anaconda3\\envs\\tool\\lib\\_threading_local.py',
   'PYMODULE'),
  ('signal', 'D:\\Anaconda3\\envs\\tool\\lib\\signal.py', 'PYMODULE'),
  ('_strptime', 'D:\\Anaconda3\\envs\\tool\\lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'D:\\Anaconda3\\envs\\tool\\lib\\datetime.py', 'PYMODULE'),
  ('calendar', 'D:\\Anaconda3\\envs\\tool\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\Anaconda3\\envs\\tool\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Anaconda3\\envs\\tool\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\Anaconda3\\envs\\tool\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\Anaconda3\\envs\\tool\\lib\\gettext.py', 'PYMODULE'),
  ('struct', 'D:\\Anaconda3\\envs\\tool\\lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\Anaconda3\\envs\\tool\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\Anaconda3\\envs\\tool\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\Anaconda3\\envs\\tool\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Anaconda3\\envs\\tool\\lib\\configparser.py',
   'PYMODULE'),
  ('email', 'D:\\Anaconda3\\envs\\tool\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Anaconda3\\envs\\tool\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib', 'D:\\Anaconda3\\envs\\tool\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('socket', 'D:\\Anaconda3\\envs\\tool\\lib\\socket.py', 'PYMODULE'),
  ('random', 'D:\\Anaconda3\\envs\\tool\\lib\\random.py', 'PYMODULE'),
  ('hashlib', 'D:\\Anaconda3\\envs\\tool\\lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'D:\\Anaconda3\\envs\\tool\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\Anaconda3\\envs\\tool\\lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Anaconda3\\envs\\tool\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Anaconda3\\envs\\tool\\lib\\getopt.py', 'PYMODULE'),
  ('quopri', 'D:\\Anaconda3\\envs\\tool\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\Anaconda3\\envs\\tool\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\Anaconda3\\envs\\tool\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Anaconda3\\envs\\tool\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'D:\\Anaconda3\\envs\\tool\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'D:\\Anaconda3\\envs\\tool\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\Anaconda3\\envs\\tool\\lib\\token.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Anaconda3\\envs\\tool\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Anaconda3\\envs\\tool\\lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'D:\\Anaconda3\\envs\\tool\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma', 'D:\\Anaconda3\\envs\\tool\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\Anaconda3\\envs\\tool\\lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Anaconda3\\envs\\tool\\lib\\fnmatch.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Anaconda3\\envs\\tool\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\Anaconda3\\envs\\tool\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\Anaconda3\\envs\\tool\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Anaconda3\\envs\\tool\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\Anaconda3\\envs\\tool\\lib\\ipaddress.py', 'PYMODULE'),
  ('getpass', 'D:\\Anaconda3\\envs\\tool\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Anaconda3\\envs\\tool\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\Anaconda3\\envs\\tool\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Anaconda3\\envs\\tool\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\Anaconda3\\envs\\tool\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Anaconda3\\envs\\tool\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\Anaconda3\\envs\\tool\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\Anaconda3\\envs\\tool\\lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\Anaconda3\\envs\\tool\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\Anaconda3\\envs\\tool\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Anaconda3\\envs\\tool\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Anaconda3\\envs\\tool\\lib\\http\\client.py',
   'PYMODULE'),
  ('decimal', 'D:\\Anaconda3\\envs\\tool\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Anaconda3\\envs\\tool\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\Anaconda3\\envs\\tool\\lib\\contextvars.py', 'PYMODULE'),
  ('numbers', 'D:\\Anaconda3\\envs\\tool\\lib\\numbers.py', 'PYMODULE'),
  ('hmac', 'D:\\Anaconda3\\envs\\tool\\lib\\hmac.py', 'PYMODULE'),
  ('tempfile', 'D:\\Anaconda3\\envs\\tool\\lib\\tempfile.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\Anaconda3\\envs\\tool\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\Anaconda3\\envs\\tool\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'D:\\Anaconda3\\envs\\tool\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Anaconda3\\envs\\tool\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\Anaconda3\\envs\\tool\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\Anaconda3\\envs\\tool\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Anaconda3\\envs\\tool\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\Anaconda3\\envs\\tool\\lib\\zipimport.py', 'PYMODULE'),
  ('inspect', 'D:\\Anaconda3\\envs\\tool\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'D:\\Anaconda3\\envs\\tool\\lib\\ast.py', 'PYMODULE'),
  ('dis', 'D:\\Anaconda3\\envs\\tool\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Anaconda3\\envs\\tool\\lib\\opcode.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Anaconda3\\envs\\tool\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Anaconda3\\envs\\tool\\lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Anaconda3\\envs\\tool\\lib\\tracemalloc.py', 'PYMODULE'),
  ('stringprep', 'D:\\Anaconda3\\envs\\tool\\lib\\stringprep.py', 'PYMODULE'),
  ('data_models', 'D:\\wowcker\\wowcker_tool\\data_models.py', 'PYMODULE'),
  ('error_handler', 'D:\\wowcker\\wowcker_tool\\error_handler.py', 'PYMODULE'),
  ('csv_processor', 'D:\\wowcker\\wowcker_tool\\csv_processor.py', 'PYMODULE'),
  ('data_allocator',
   'D:\\wowcker\\wowcker_tool\\data_allocator.py',
   'PYMODULE'),
  ('numpy',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('json', 'D:\\Anaconda3\\envs\\tool\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\Anaconda3\\envs\\tool\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Anaconda3\\envs\\tool\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Anaconda3\\envs\\tool\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('__future__', 'D:\\Anaconda3\\envs\\tool\\lib\\__future__.py', 'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('platform', 'D:\\Anaconda3\\envs\\tool\\lib\\platform.py', 'PYMODULE'),
  ('numpy.distutils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('doctest', 'D:\\Anaconda3\\envs\\tool\\lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\Anaconda3\\envs\\tool\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\Anaconda3\\envs\\tool\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Anaconda3\\envs\\tool\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server',
   'D:\\Anaconda3\\envs\\tool\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\Anaconda3\\envs\\tool\\lib\\socketserver.py',
   'PYMODULE'),
  ('html', 'D:\\Anaconda3\\envs\\tool\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Anaconda3\\envs\\tool\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Anaconda3\\envs\\tool\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\Anaconda3\\envs\\tool\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\Anaconda3\\envs\\tool\\lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'D:\\Anaconda3\\envs\\tool\\lib\\sysconfig.py', 'PYMODULE'),
  ('glob', 'D:\\Anaconda3\\envs\\tool\\lib\\glob.py', 'PYMODULE'),
  ('code', 'D:\\Anaconda3\\envs\\tool\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Anaconda3\\envs\\tool\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\Anaconda3\\envs\\tool\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\Anaconda3\\envs\\tool\\lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'D:\\Anaconda3\\envs\\tool\\lib\\difflib.py', 'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('_dummy_thread',
   'D:\\Anaconda3\\envs\\tool\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Anaconda3\\envs\\tool\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Anaconda3\\envs\\tool\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Anaconda3\\envs\\tool\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Anaconda3\\envs\\tool\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Anaconda3\\envs\\tool\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Anaconda3\\envs\\tool\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Anaconda3\\envs\\tool\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\Anaconda3\\envs\\tool\\lib\\dataclasses.py', 'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('logging',
   'D:\\Anaconda3\\envs\\tool\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('typing', 'D:\\Anaconda3\\envs\\tool\\lib\\typing.py', 'PYMODULE'),
  ('pathlib', 'D:\\Anaconda3\\envs\\tool\\lib\\pathlib.py', 'PYMODULE'),
  ('pandas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util.testing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('uuid', 'D:\\Anaconda3\\envs\\tool\\lib\\uuid.py', 'PYMODULE'),
  ('ctypes.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Anaconda3\\envs\\tool\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas.core.indexes.numeric',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\numeric.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('six', 'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('dateutil.tz',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('fractions', 'D:\\Anaconda3\\envs\\tool\\lib\\fractions.py', 'PYMODULE'),
  ('dateutil._common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.compat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\compat.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlwt',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_xlwt.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.date_converters',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\date_converters.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Anaconda3\\envs\\tool\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Anaconda3\\envs\\tool\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Anaconda3\\envs\\tool\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.compat.chainmap',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\chainmap.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.describe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\describe.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Anaconda3\\envs\\tool\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pytz',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Anaconda3\\envs\\tool\\lib\\tkinter\\constants.py',
   'PYMODULE')],
 [('python38.dll', 'D:\\Anaconda3\\envs\\tool\\python38.dll', 'BINARY'),
  ('numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('select.pyd', 'D:\\Anaconda3\\envs\\tool\\DLLs\\select.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Anaconda3\\envs\\tool\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'D:\\Anaconda3\\envs\\tool\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Anaconda3\\envs\\tool\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'D:\\Anaconda3\\envs\\tool\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Anaconda3\\envs\\tool\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Anaconda3\\envs\\tool\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\Anaconda3\\envs\\tool\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\Anaconda3\\envs\\tool\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Anaconda3\\envs\\tool\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Anaconda3\\envs\\tool\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Anaconda3\\envs\\tool\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Anaconda3\\envs\\tool\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Anaconda3\\envs\\tool\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Anaconda3\\envs\\tool\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Anaconda3\\envs\\tool\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\json.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\join.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\index.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'D:\\Anaconda3\\envs\\tool\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\Anaconda3\\envs\\tool\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\Anaconda3\\envs\\tool\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\Anaconda3\\envs\\tool\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libffi-7.dll', 'D:\\Anaconda3\\envs\\tool\\DLLs\\libffi-7.dll', 'BINARY'),
  ('sqlite3.dll',
   'D:\\Anaconda3\\envs\\tool\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\MSVCP140.dll',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('tcl86t.dll', 'D:\\Anaconda3\\envs\\tool\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\Anaconda3\\envs\\tool\\DLLs\\tk86t.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\Anaconda3\\envs\\tool\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Anaconda3\\envs\\tool\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\Anaconda3\\envs\\tool\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tk_data\\tk.tcl', 'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\Anaconda3\\envs\\tool\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('base_library.zip',
   'D:\\wowcker\\wowcker_tool\\build\\wowcker_tool\\base_library.zip',
   'DATA')],
 [('functools', 'D:\\Anaconda3\\envs\\tool\\lib\\functools.py', 'PYMODULE'),
  ('locale', 'D:\\Anaconda3\\envs\\tool\\lib\\locale.py', 'PYMODULE'),
  ('operator', 'D:\\Anaconda3\\envs\\tool\\lib\\operator.py', 'PYMODULE'),
  ('posixpath', 'D:\\Anaconda3\\envs\\tool\\lib\\posixpath.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\Anaconda3\\envs\\tool\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('copyreg', 'D:\\Anaconda3\\envs\\tool\\lib\\copyreg.py', 'PYMODULE'),
  ('heapq', 'D:\\Anaconda3\\envs\\tool\\lib\\heapq.py', 'PYMODULE'),
  ('linecache', 'D:\\Anaconda3\\envs\\tool\\lib\\linecache.py', 'PYMODULE'),
  ('sre_parse', 'D:\\Anaconda3\\envs\\tool\\lib\\sre_parse.py', 'PYMODULE'),
  ('abc', 'D:\\Anaconda3\\envs\\tool\\lib\\abc.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\Anaconda3\\envs\\tool\\lib\\sre_constants.py',
   'PYMODULE'),
  ('genericpath', 'D:\\Anaconda3\\envs\\tool\\lib\\genericpath.py', 'PYMODULE'),
  ('reprlib', 'D:\\Anaconda3\\envs\\tool\\lib\\reprlib.py', 'PYMODULE'),
  ('_bootlocale', 'D:\\Anaconda3\\envs\\tool\\lib\\_bootlocale.py', 'PYMODULE'),
  ('re', 'D:\\Anaconda3\\envs\\tool\\lib\\re.py', 'PYMODULE'),
  ('keyword', 'D:\\Anaconda3\\envs\\tool\\lib\\keyword.py', 'PYMODULE'),
  ('stat', 'D:\\Anaconda3\\envs\\tool\\lib\\stat.py', 'PYMODULE'),
  ('enum', 'D:\\Anaconda3\\envs\\tool\\lib\\enum.py', 'PYMODULE'),
  ('types', 'D:\\Anaconda3\\envs\\tool\\lib\\types.py', 'PYMODULE'),
  ('codecs', 'D:\\Anaconda3\\envs\\tool\\lib\\codecs.py', 'PYMODULE'),
  ('warnings', 'D:\\Anaconda3\\envs\\tool\\lib\\warnings.py', 'PYMODULE'),
  ('ntpath', 'D:\\Anaconda3\\envs\\tool\\lib\\ntpath.py', 'PYMODULE'),
  ('sre_compile', 'D:\\Anaconda3\\envs\\tool\\lib\\sre_compile.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\Anaconda3\\envs\\tool\\lib\\_weakrefset.py', 'PYMODULE'),
  ('weakref', 'D:\\Anaconda3\\envs\\tool\\lib\\weakref.py', 'PYMODULE'),
  ('traceback', 'D:\\Anaconda3\\envs\\tool\\lib\\traceback.py', 'PYMODULE'),
  ('io', 'D:\\Anaconda3\\envs\\tool\\lib\\io.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_centeuro',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_centeuro.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\Anaconda3\\envs\\tool\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('collections.abc',
   'D:\\Anaconda3\\envs\\tool\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\Anaconda3\\envs\\tool\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('os', 'D:\\Anaconda3\\envs\\tool\\lib\\os.py', 'PYMODULE')])
