#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分配算法
实现新数据平均分配到ID(1-5)的功能
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any
import logging
import random

logger = logging.getLogger(__name__)

class DataAllocator:
    """数据分配器"""
    
    def __init__(self):
        self.user_ids = [1, 2, 3, 4, 5, 6, 7, 8]
    
    def allocate_new_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        为新数据分配ID
        
        Args:
            df: 包含id=0的新数据的DataFrame
            
        Returns:
            分配完成的DataFrame
        """
        if df is None or df.empty:
            return df
        
        # 复制数据避免修改原始数据
        result_df = df.copy()
        
        # 找出需要分配的新数据（id=0的行）
        new_data_mask = result_df['id'] == 0
        new_data_indices = result_df[new_data_mask].index.tolist()
        
        if not new_data_indices:
            logger.info("没有需要分配的新数据")
            return result_df
        
        # 获取当前各ID的数据统计
        current_counts = self.get_current_allocation_counts(result_df)
        logger.info(f"当前各ID数据统计: {current_counts}")
        
        # 执行平均分配
        allocated_ids = self.perform_balanced_allocation(new_data_indices, current_counts)
        
        # 更新DataFrame
        for idx, user_id in zip(new_data_indices, allocated_ids):
            result_df.at[idx, 'id'] = user_id
        
        # 记录分配结果
        final_counts = self.get_current_allocation_counts(result_df)
        logger.info(f"分配后各ID数据统计: {final_counts}")
        logger.info(f"本次分配了 {len(new_data_indices)} 条新数据")
        
        return result_df
    
    def get_current_allocation_counts(self, df: pd.DataFrame) -> Dict[int, int]:
        """获取当前各ID的数据统计"""
        counts = {}
        for user_id in self.user_ids:
            # 只统计非新数据（id != 0）
            count = len(df[(df['id'] == user_id) & (df['id'] != 0)])
            counts[user_id] = count
        return counts
    
    def perform_balanced_allocation(self, indices: List[int], current_counts: Dict[int, int]) -> List[int]:
        """
        执行平衡分配算法
        
        Args:
            indices: 需要分配的数据索引列表
            current_counts: 当前各ID的数据统计
            
        Returns:
            分配的ID列表
        """
        num_new_items = len(indices)
        allocated_ids = []
        
        # 创建当前计数的副本用于计算
        working_counts = current_counts.copy()
        
        # 使用轮询算法进行平均分配
        for i in range(num_new_items):
            # 找到当前数据最少的ID
            min_count = min(working_counts.values())
            candidates = [uid for uid, count in working_counts.items() if count == min_count]
            
            # 如果有多个候选者，随机选择一个
            selected_id = random.choice(candidates)
            allocated_ids.append(selected_id)
            
            # 更新计数
            working_counts[selected_id] += 1
        
        return allocated_ids
    
    def get_allocation_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """获取分配统计信息"""
        if df is None or df.empty:
            return {}
        
        stats = {}
        total_count = len(df[df['id'] != 0])  # 排除未分配的数据
        
        for user_id in self.user_ids:
            count = len(df[df['id'] == user_id])
            percentage = (count / total_count * 100) if total_count > 0 else 0
            stats[f'ID_{user_id}'] = {
                'count': count,
                'percentage': round(percentage, 2)
            }
        
        # 计算分配均衡度（标准差）
        counts = [stats[f'ID_{uid}']['count'] for uid in self.user_ids]
        balance_score = np.std(counts) if counts else 0
        stats['balance_score'] = round(balance_score, 2)
        stats['total_allocated'] = total_count
        
        return stats
    
    def rebalance_allocation(self, df: pd.DataFrame, force_rebalance: bool = False) -> pd.DataFrame:
        """
        重新平衡数据分配
        
        Args:
            df: 要重新平衡的DataFrame
            force_rebalance: 是否强制重新平衡
            
        Returns:
            重新平衡后的DataFrame
        """
        if df is None or df.empty:
            return df
        
        # 获取当前统计
        current_stats = self.get_allocation_statistics(df)
        balance_score = current_stats.get('balance_score', 0)
        
        # 如果不强制重新平衡且当前已经相对平衡，则不进行操作
        if not force_rebalance and balance_score <= 1.0:
            logger.info(f"当前分配已经相对平衡 (balance_score: {balance_score})，跳过重新平衡")
            return df
        
        logger.info(f"开始重新平衡数据分配 (当前balance_score: {balance_score})")
        
        # 复制数据
        result_df = df.copy()
        
        # 获取所有已分配的数据
        allocated_data = result_df[result_df['id'] != 0].copy()
        
        if allocated_data.empty:
            return result_df
        
        # 重新分配所有数据
        indices = allocated_data.index.tolist()
        random.shuffle(indices)  # 随机打乱顺序
        
        # 平均分配
        num_items = len(indices)
        items_per_id = num_items // len(self.user_ids)
        remainder = num_items % len(self.user_ids)
        
        current_idx = 0
        for i, user_id in enumerate(self.user_ids):
            # 计算当前ID应该分配的数量
            count_for_this_id = items_per_id + (1 if i < remainder else 0)
            
            # 分配数据
            for j in range(count_for_this_id):
                if current_idx < len(indices):
                    result_df.at[indices[current_idx], 'id'] = user_id
                    current_idx += 1
        
        # 记录重新平衡结果
        final_stats = self.get_allocation_statistics(result_df)
        logger.info(f"重新平衡完成，新的balance_score: {final_stats.get('balance_score', 0)}")
        
        return result_df

def create_allocator() -> DataAllocator:
    """创建数据分配器实例"""
    return DataAllocator()
